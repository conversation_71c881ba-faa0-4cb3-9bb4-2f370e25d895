# Admin Activity Cashback Schema - Types and Inputs

# Task Types
type ActivityTask {
  id: ID!
  categoryId: ID!
  category: TaskCategory!
  name: String!
  description: String
  taskType: TaskType!
  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String
  actionTarget: String
  verificationMethod: String
  externalLink: String
  startDate: Int64
  endDate: Int64
  sortOrder: Int!
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
}

type TaskCategory {
  id: ID!
  name: String!
  displayName: String!
  description: String
  icon: String
  isActive: Boolean!
  sortOrder: Int!
  createdAt: Time!
  updatedAt: Time!
}

# Enums
enum TaskType {
  TRADING
  SOCIAL
  REFERRAL
  DEPOSIT
  WITHDRAWAL
  KYC
  CUSTOM
}

enum TaskFrequency {
  ONCE
  DAILY
  WEEKLY
  MONTHLY
  UNLIMITED
}

enum TaskIdentifier {
  # Daily Tasks
  DAILY_CHECKIN
  MEME_TRADE_DAILY
  PERPETUAL_TRADE_DAILY
  MARKET_PAGE_VIEW
  CONSECUTIVE_CHECKIN
  CONSECUTIVE_TRADING_DAYS

  # Community Tasks
  TWITTER_FOLLOW
  TWITTER_RETWEET
  TWITTER_LIKE
  TELEGRAM_JOIN
  INVITE_FRIENDS
  SHARE_REFERRAL

  # Trading Tasks
  TRADING_POINTS
  ACCUMULATED_TRADING_10K
  ACCUMULATED_TRADING_50K
  ACCUMULATED_TRADING_100K
  ACCUMULATED_TRADING_500K
}

# User Tier Types
type UserTierInfo {
  userId: ID!
  email: String
  currentTier: TierBenefit
  totalPoints: Int!
  availableCashback: Float!
  totalCashbackClaimed: Float!
  nextTier: TierBenefit
  pointsToNextTier: Int
  createdAt: Time!
  lastActivityAt: Time
}

type TierBenefit {
  id: ID!
  tierLevel: Int!
  tierName: String!
  minPoints: Int!
  cashbackPercentage: Float!
  benefitsDescription: String
  tierColor: String
  tierIcon: String
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
}

type TierBenefitResponse {
  success: Boolean!
  message: String!
  data: TierBenefit
}

# Input Types
input CreateTaskInput {
  categoryId: ID!
  name: String!
  description: String
  taskType: TaskType!
  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String
  actionTarget: String
  verificationMethod: String
  externalLink: String
  startDate: Int64
  endDate: Int64
  sortOrder: Int
}

input UpdateTaskInput {
  id: ID!
  categoryId: ID
  name: String
  description: String
  taskType: TaskType
  frequency: TaskFrequency
  taskIdentifier: TaskIdentifier
  points: Int
  maxCompletions: Int
  resetPeriod: String
  conditions: String
  actionTarget: String
  verificationMethod: String
  externalLink: String
  startDate: Int64
  endDate: Int64
  sortOrder: Int
  isActive: Boolean
}

input CreateTaskCategoryInput {
  name: String!
  displayName: String!
  description: String
  icon: String
  sortOrder: Int
}

input UpdateTaskCategoryInput {
  id: ID!
  name: String
  displayName: String
  description: String
  icon: String
  isActive: Boolean
  sortOrder: Int
}

input CreateTierBenefitInput {
  tierLevel: Int!
  tierName: String!
  minPoints: Int!
  cashbackPercentage: Float!
  benefitsDescription: String
  tierColor: String
  tierIcon: String
}

input UpdateTierBenefitInput {
  id: ID!
  tierLevel: Int
  tierName: String
  minPoints: Int
  cashbackPercentage: Float
  benefitsDescription: String
  tierColor: String
  tierIcon: String
  isActive: Boolean
}

# Admin input types
input AdminStatsInput {
  startDate: Time!
  endDate: Time!
}

# Admin response types
type AdminTaskCompletionStatsResponse {
  success: Boolean!
  message: String!
  data: AdminTaskCompletionStats
}

type AdminTaskCompletionStats {
  taskCompletions: [TaskCompletionStat!]!
  startDate: Time!
  endDate: Time!
  totalTasks: Int!
}

type TaskCompletionStat {
  taskName: String!
  completionCount: Int!
}

type AdminUserActivityStatsResponse {
  success: Boolean!
  message: String!
  data: AdminUserActivityStats
}

type AdminUserActivityStats {
  dailyCompletions: [DailyCompletionStat!]!
  startDate: Time!
  endDate: Time!
}

type DailyCompletionStat {
  date: String!
  completionCount: Int!
}

type AdminTierDistributionResponse {
  success: Boolean!
  message: String!
  data: [TierDistributionStat!]!
}

type TierDistributionStat {
  tierLevel: Int!
  userCount: Int!
}
